@import "tailwindcss";

@layer components {
  .font-cairo {
    font-family: "Cairo", serif;
    font-optical-sizing: auto;
    font-weight: 700;
    font-style: normal;
    font-variation-settings: "slnt" 0;
  }

  .bg-custom-image {
    background-image: url("/homepage/home.jpg");
    background-size: cover;
    background-position: 0px -200px;
  }

  @media (max-width: 768px) {
    .bg-custom-image {
      background-image: url("/homepage/home.jpg");
      background-size: cover;
      background-position: center;
    }
  }
}

:root {
  --font-geist-sans: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  --font-geist-mono: "Courier New", monospace;

  font-family: var(--font-geist-sans);
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #ffffff;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
